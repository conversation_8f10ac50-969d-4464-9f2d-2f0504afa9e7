{"kiroAgent.configureMCP": "Disabled", "WillLuke.nextjs.addTypesOnSave": true, "WillLuke.nextjs.hasPrompted": true, "sqltools.connections": [{"pgOptions": {"ssl": {"rejectUnauthorized": false}}, "ssh": "Disabled", "previewLimit": 100, "server": "localhost", "driver": "PostgreSQL", "connectString": "***********************************************************************************************************************************************", "name": "interchatprod"}, {"ssh": "Disabled", "previewLimit": 50, "server": "localhost", "driver": "PostgreSQL", "connectString": "postgresql://postgres:abcd123@localhost:5432/inttest", "name": "dev"}]}