{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5"}}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "recommended": true, "suspicious": {"noUnknownAtRules": "off"}}}}