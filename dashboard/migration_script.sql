-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "public";

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."AppealStatus" AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED');

-- CreateEnum
CREATE TYPE "public"."BlockWordAction" AS ENUM ('BLOCK_MESSAGE', 'SEND_ALERT', 'WARN', 'MUTE', 'BAN', 'BLACKLIST');

-- CreateEnum
CREATE TYPE "public"."HubActivityLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH');

-- CreateEnum
CREATE TYPE "public"."InfractionStatus" AS ENUM ('ACTIVE', 'REVOKED', 'APPEALED');

-- CreateEnum
CREATE TYPE "public"."InfractionType" AS ENUM ('BAN', 'BLACKLIST', 'MUTE', 'WARNING');

-- CreateEnum
CREATE TYPE "public"."ReportStatus" AS ENUM ('PENDING', 'RESOLVED', 'IGNORED');

-- CreateEnum
CREATE TYPE "public"."Role" AS ENUM ('MODERATOR', 'MANAGER');

-- CreateEnum
CREATE TYPE "public"."Badges" AS ENUM ('VOTER', 'SUPPORTER', 'TRANSLATOR', 'DEVELOPER', 'STAFF', 'BETA_TESTER');

-- CreateEnum
CREATE TYPE "public"."BlacklistType" AS ENUM ('PERMANENT', 'TEMPORARY');

-- CreateEnum
CREATE TYPE "public"."LeaderboardPeriod" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'ALL_TIME');

-- CreateEnum
CREATE TYPE "public"."LeaderboardType" AS ENUM ('USER', 'SERVER', 'HUB');

-- CreateEnum
CREATE TYPE "public"."PatternMatchType" AS ENUM ('EXACT', 'PREFIX', 'SUFFIX', 'WILDCARD');

-- CreateTable
CREATE TABLE "public"."Achievement" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "badgeEmoji" TEXT NOT NULL,
    "badgeUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "threshold" INTEGER NOT NULL,
    "secret" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Achievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AntiSwearPattern" (
    "id" TEXT NOT NULL,
    "ruleId" TEXT NOT NULL,
    "pattern" TEXT NOT NULL,
    "matchType" "public"."PatternMatchType" NOT NULL DEFAULT 'EXACT',

    CONSTRAINT "AntiSwearPattern_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AntiSwearRule" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "muteDurationMinutes" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "actions" "public"."BlockWordAction"[] DEFAULT ARRAY[]::"public"."BlockWordAction"[],

    CONSTRAINT "AntiSwearRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Appeal" (
    "infractionId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "status" "public"."AppealStatus" NOT NULL DEFAULT 'PENDING',

    CONSTRAINT "Appeal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BlockWord" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "words" TEXT NOT NULL,
    "actions" "public"."BlockWordAction"[],

    CONSTRAINT "BlockWord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Broadcast" (
    "id" TEXT NOT NULL,
    "messageId" TEXT NOT NULL,
    "channelId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guildId" TEXT NOT NULL,

    CONSTRAINT "Broadcast_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Connection" (
    "id" TEXT NOT NULL,
    "channelId" TEXT NOT NULL,
    "invite" TEXT,
    "webhookURL" TEXT NOT NULL,
    "serverId" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastActive" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "parentId" TEXT,
    "connected" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "Connection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Hub" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "ownerId" TEXT NOT NULL,
    "iconUrl" TEXT NOT NULL,
    "shortDescription" VARCHAR(100),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastActive" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastNameChange" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "bannerUrl" TEXT,
    "welcomeMessage" TEXT,
    "language" TEXT,
    "region" TEXT,
    "settings" INTEGER NOT NULL DEFAULT 0,
    "appealCooldownHours" INTEGER NOT NULL DEFAULT 168,
    "weeklyMessageCount" INTEGER NOT NULL DEFAULT 0,
    "private" BOOLEAN NOT NULL DEFAULT true,
    "locked" BOOLEAN NOT NULL DEFAULT false,
    "nsfw" BOOLEAN NOT NULL DEFAULT false,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "partnered" BOOLEAN NOT NULL DEFAULT false,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "rules" TEXT[],
    "activityLevel" "public"."HubActivityLevel" NOT NULL DEFAULT 'LOW',

    CONSTRAINT "Hub_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubActivityMetrics" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "messagesLast24h" INTEGER NOT NULL DEFAULT 0,
    "activeUsersLast24h" INTEGER NOT NULL DEFAULT 0,
    "newConnectionsLast24h" INTEGER NOT NULL DEFAULT 0,
    "messagesLast7d" INTEGER NOT NULL DEFAULT 0,
    "activeUsersLast7d" INTEGER NOT NULL DEFAULT 0,
    "newConnectionsLast7d" INTEGER NOT NULL DEFAULT 0,
    "memberGrowthRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "engagementRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,

    CONSTRAINT "HubActivityMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubInvite" (
    "code" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "expires" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "maxUses" INTEGER NOT NULL DEFAULT 0,
    "uses" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "HubInvite_pkey" PRIMARY KEY ("code")
);

-- CreateTable
CREATE TABLE "public"."HubLogConfig" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "modLogsChannelId" TEXT,
    "modLogsRoleId" TEXT,
    "joinLeavesChannelId" TEXT,
    "joinLeavesRoleId" TEXT,
    "appealsChannelId" TEXT,
    "appealsRoleId" TEXT,
    "reportsChannelId" TEXT,
    "reportsRoleId" TEXT,
    "networkAlertsChannelId" TEXT,
    "networkAlertsRoleId" TEXT,
    "messageModerationChannelId" TEXT,
    "messageModerationRoleId" TEXT,

    CONSTRAINT "HubLogConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubModerator" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "role" "public"."Role" NOT NULL,

    CONSTRAINT "HubModerator_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubReview" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "rating" INTEGER NOT NULL,
    "text" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "HubReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubRulesAcceptance" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "acceptedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "HubRulesAcceptance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubUpvote" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "hubId" TEXT NOT NULL,

    CONSTRAINT "HubUpvote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Infraction" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "moderatorId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "userId" TEXT,
    "serverId" TEXT,
    "serverName" TEXT,
    "type" "public"."InfractionType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "public"."InfractionStatus" NOT NULL DEFAULT 'ACTIVE',
    "notified" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Infraction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Message" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "imageUrl" TEXT,
    "channelId" TEXT NOT NULL,
    "guildId" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "referredMessageId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReputationLog" (
    "id" TEXT NOT NULL,
    "giverId" TEXT NOT NULL,
    "receiverId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "automatic" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ReputationLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ServerData" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "lastMessageAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "inviteCode" TEXT,
    "messageCount" INTEGER NOT NULL DEFAULT 0,
    "iconUrl" TEXT,

    CONSTRAINT "ServerData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Tag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "category" TEXT,
    "description" TEXT,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isOfficial" BOOLEAN NOT NULL DEFAULT false,
    "usageCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "Tag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "image" TEXT,
    "lastMessageAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "inboxLastReadDate" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "activityLevel" "public"."HubActivityLevel",
    "lastHubJoinAt" TIMESTAMP(3),
    "email" TEXT,
    "emailVerified" BOOLEAN,
    "showBadges" BOOLEAN NOT NULL DEFAULT true,
    "mentionOnReply" BOOLEAN NOT NULL DEFAULT true,
    "showNsfwHubs" BOOLEAN NOT NULL DEFAULT false,
    "voteCount" INTEGER NOT NULL DEFAULT 0,
    "reputation" INTEGER NOT NULL DEFAULT 0,
    "messageCount" INTEGER NOT NULL DEFAULT 0,
    "hubJoinCount" INTEGER NOT NULL DEFAULT 0,
    "hubEngagementScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "locale" TEXT,
    "lastVoted" TIMESTAMP(3),
    "badges" "public"."Badges"[] DEFAULT ARRAY[]::"public"."Badges"[],
    "preferredLanguages" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserAchievement" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "achievementId" TEXT NOT NULL,
    "unlockedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserAchievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserAchievementProgress" (
    "userId" TEXT NOT NULL,
    "achievementId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "currentValue" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "UserAchievementProgress_pkey" PRIMARY KEY ("userId","achievementId")
);

-- CreateTable
CREATE TABLE "public"."Blacklist" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "moderatorId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "type" "public"."BlacklistType" NOT NULL DEFAULT 'PERMANENT',

    CONSTRAINT "Blacklist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."LeaderboardEntry" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "serverId" TEXT NOT NULL,
    "period" "public"."LeaderboardPeriod" NOT NULL,
    "type" "public"."LeaderboardType" NOT NULL,
    "lastActivityAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "messageCount" INTEGER NOT NULL DEFAULT 0,
    "score" INTEGER NOT NULL DEFAULT 0,
    "rank" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "LeaderboardEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ServerBlacklist" (
    "id" TEXT NOT NULL,
    "serverId" TEXT NOT NULL,
    "moderatorId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "duration" INTEGER,
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "type" "public"."BlacklistType" NOT NULL DEFAULT 'PERMANENT',

    CONSTRAINT "ServerBlacklist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."DevAlerts" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "thumbnailUrl" TEXT,

    CONSTRAINT "DevAlerts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubAnnouncement" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "frequencyMs" INTEGER NOT NULL,
    "previousAnnouncement" TIMESTAMP(3),
    "nextAnnouncement" TIMESTAMP(3),
    "imageUrl" TEXT,
    "thumbnailUrl" TEXT,

    CONSTRAINT "HubAnnouncement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."GlobalReport" (
    "id" TEXT NOT NULL,
    "reporterId" TEXT NOT NULL,
    "reportedUserId" TEXT NOT NULL,
    "reportedServerId" TEXT NOT NULL,
    "messageId" TEXT,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "handledBy" TEXT,
    "status" "public"."ReportStatus" NOT NULL DEFAULT 'PENDING',
    "handledAt" TIMESTAMP(3),

    CONSTRAINT "GlobalReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubReport" (
    "id" TEXT NOT NULL,
    "hubId" TEXT NOT NULL,
    "reporterId" TEXT NOT NULL,
    "reportedUserId" TEXT NOT NULL,
    "reportedServerId" TEXT NOT NULL,
    "messageId" TEXT,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "handledBy" TEXT,
    "status" "public"."ReportStatus" NOT NULL DEFAULT 'PENDING',
    "handledAt" TIMESTAMP(3),

    CONSTRAINT "HubReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."HubMessageReaction" (
    "id" TEXT NOT NULL,
    "messageId" TEXT NOT NULL,
    "emoji" VARCHAR(64) NOT NULL,
    "users" TEXT[],

    CONSTRAINT "HubMessageReaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AntiSwearWhitelist" (
    "id" TEXT NOT NULL,
    "ruleId" TEXT NOT NULL,
    "word" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AntiSwearWhitelist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Account" (
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("provider","providerAccountId")
);

-- CreateTable
CREATE TABLE "public"."Session" (
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."_HubToTag" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_HubToTag_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "AntiSwearPattern_ruleId_idx" ON "public"."AntiSwearPattern"("ruleId");

-- CreateIndex
CREATE INDEX "AntiSwearPattern_matchType_idx" ON "public"."AntiSwearPattern"("matchType");

-- CreateIndex
CREATE INDEX "AntiSwearRule_hubId_idx" ON "public"."AntiSwearRule"("hubId");

-- CreateIndex
CREATE UNIQUE INDEX "AntiSwearRule_hubId_name_key" ON "public"."AntiSwearRule"("hubId", "name");

-- CreateIndex
CREATE INDEX "Appeal_infractionId_idx" ON "public"."Appeal"("infractionId");

-- CreateIndex
CREATE INDEX "Appeal_status_idx" ON "public"."Appeal"("status");

-- CreateIndex
CREATE INDEX "Appeal_userId_idx" ON "public"."Appeal"("userId");

-- CreateIndex
CREATE INDEX "Appeal_createdAt_idx" ON "public"."Appeal"("createdAt");

-- CreateIndex
CREATE INDEX "BlockWord_hubId_idx" ON "public"."BlockWord"("hubId");

-- CreateIndex
CREATE UNIQUE INDEX "BlockWord_hubId_name_key" ON "public"."BlockWord"("hubId", "name");

-- CreateIndex
CREATE INDEX "Broadcast_channelId_idx" ON "public"."Broadcast"("channelId");

-- CreateIndex
CREATE INDEX "Broadcast_messageId_idx" ON "public"."Broadcast"("messageId");

-- CreateIndex
CREATE INDEX "Broadcast_createdAt_idx" ON "public"."Broadcast"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "Broadcast_id_messageId_channelId_createdAt_idx" ON "public"."Broadcast"("id", "messageId", "channelId", "createdAt");

-- CreateIndex
CREATE INDEX "Broadcast_messageId_channelId_idx" ON "public"."Broadcast"("messageId", "channelId");

-- CreateIndex
CREATE UNIQUE INDEX "Connection_channelId_key" ON "public"."Connection"("channelId");

-- CreateIndex
CREATE INDEX "Connection_hubId_channelId_idx" ON "public"."Connection"("hubId", "channelId");

-- CreateIndex
CREATE INDEX "Connection_lastActive_idx" ON "public"."Connection"("lastActive");

-- CreateIndex
CREATE INDEX "Connection_hubId_idx" ON "public"."Connection"("hubId");

-- CreateIndex
CREATE INDEX "Connection_serverId_idx" ON "public"."Connection"("serverId");

-- CreateIndex
CREATE UNIQUE INDEX "Connection_channelId_serverId_key" ON "public"."Connection"("channelId", "serverId");

-- CreateIndex
CREATE UNIQUE INDEX "Connection_hubId_serverId_key" ON "public"."Connection"("hubId", "serverId");

-- CreateIndex
CREATE UNIQUE INDEX "Hub_name_key" ON "public"."Hub"("name");

-- CreateIndex
CREATE INDEX "Hub_ownerId_idx" ON "public"."Hub"("ownerId");

-- CreateIndex
CREATE INDEX "Hub_activityLevel_idx" ON "public"."Hub"("activityLevel");

-- CreateIndex
CREATE INDEX "Hub_language_idx" ON "public"."Hub"("language");

-- CreateIndex
CREATE INDEX "Hub_nsfw_idx" ON "public"."Hub"("nsfw");

-- CreateIndex
CREATE INDEX "Hub_verified_featured_private_idx" ON "public"."Hub"("verified", "featured", "private");

-- CreateIndex
CREATE INDEX "Hub_weeklyMessageCount_idx" ON "public"."Hub"("weeklyMessageCount");

-- CreateIndex
CREATE INDEX "Hub_private_verified_partnered_idx" ON "public"."Hub"("private", "verified", "partnered");

-- CreateIndex
CREATE INDEX "Hub_private_nsfw_activityLevel_idx" ON "public"."Hub"("private", "nsfw", "activityLevel");

-- CreateIndex
CREATE INDEX "Hub_private_language_region_idx" ON "public"."Hub"("private", "language", "region");

-- CreateIndex
CREATE INDEX "Hub_lastActive_weeklyMessageCount_idx" ON "public"."Hub"("lastActive", "weeklyMessageCount");

-- CreateIndex
CREATE INDEX "Hub_createdAt_weeklyMessageCount_idx" ON "public"."Hub"("createdAt", "weeklyMessageCount");

-- CreateIndex
CREATE UNIQUE INDEX "HubActivityMetrics_hubId_key" ON "public"."HubActivityMetrics"("hubId");

-- CreateIndex
CREATE INDEX "HubActivityMetrics_hubId_idx" ON "public"."HubActivityMetrics"("hubId");

-- CreateIndex
CREATE INDEX "HubActivityMetrics_lastUpdated_idx" ON "public"."HubActivityMetrics"("lastUpdated");

-- CreateIndex
CREATE UNIQUE INDEX "HubInvite_code_key" ON "public"."HubInvite"("code");

-- CreateIndex
CREATE INDEX "HubInvite_hubId_idx" ON "public"."HubInvite"("hubId");

-- CreateIndex
CREATE UNIQUE INDEX "HubLogConfig_hubId_key" ON "public"."HubLogConfig"("hubId");

-- CreateIndex
CREATE INDEX "HubLogConfig_hubId_idx" ON "public"."HubLogConfig"("hubId");

-- CreateIndex
CREATE INDEX "HubModerator_userId_idx" ON "public"."HubModerator"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "HubModerator_hubId_userId_key" ON "public"."HubModerator"("hubId", "userId");

-- CreateIndex
CREATE INDEX "HubReview_userId_idx" ON "public"."HubReview"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "HubReview_hubId_userId_key" ON "public"."HubReview"("hubId", "userId");

-- CreateIndex
CREATE INDEX "HubRulesAcceptance_hubId_userId_idx" ON "public"."HubRulesAcceptance"("hubId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "HubRulesAcceptance_userId_hubId_key" ON "public"."HubRulesAcceptance"("userId", "hubId");

-- CreateIndex
CREATE INDEX "HubUpvote_userId_idx" ON "public"."HubUpvote"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "HubUpvote_hubId_userId_key" ON "public"."HubUpvote"("hubId", "userId");

-- CreateIndex
CREATE INDEX "Infraction_status_hubId_idx" ON "public"."Infraction"("status", "hubId");

-- CreateIndex
CREATE INDEX "Infraction_expiresAt_idx" ON "public"."Infraction"("expiresAt");

-- CreateIndex
CREATE INDEX "Infraction_serverId_idx" ON "public"."Infraction"("serverId");

-- CreateIndex
CREATE INDEX "Infraction_type_idx" ON "public"."Infraction"("type");

-- CreateIndex
CREATE INDEX "Infraction_userId_idx" ON "public"."Infraction"("userId");

-- CreateIndex
CREATE INDEX "Message_authorId_idx" ON "public"."Message"("authorId");

-- CreateIndex
CREATE INDEX "Message_createdAt_idx" ON "public"."Message"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "Message_hubId_idx" ON "public"."Message"("hubId");

-- CreateIndex
CREATE INDEX "Message_referredMessageId_idx" ON "public"."Message"("referredMessageId");

-- CreateIndex
CREATE INDEX "Message_guildId_authorId_idx" ON "public"."Message"("guildId", "authorId");

-- CreateIndex
CREATE INDEX "Message_guildId_hubId_idx" ON "public"."Message"("guildId", "hubId");

-- CreateIndex
CREATE INDEX "Message_guildId_idx" ON "public"."Message"("guildId");

-- CreateIndex
CREATE INDEX "Message_hubId_createdAt_idx" ON "public"."Message"("hubId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "Message_author_timestamp_idx" ON "public"."Message"("authorId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "Message_channel_timestamp_idx" ON "public"."Message"("channelId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "ReputationLog_giverId_idx" ON "public"."ReputationLog"("giverId");

-- CreateIndex
CREATE INDEX "ReputationLog_receiverId_idx" ON "public"."ReputationLog"("receiverId");

-- CreateIndex
CREATE INDEX "ServerData_createdAt_idx" ON "public"."ServerData"("createdAt");

-- CreateIndex
CREATE INDEX "ServerData_lastMessageAt_idx" ON "public"."ServerData"("lastMessageAt" DESC);

-- CreateIndex
CREATE INDEX "ServerData_messageCount_idx" ON "public"."ServerData"("messageCount");

-- CreateIndex
CREATE UNIQUE INDEX "Tag_name_key" ON "public"."Tag"("name");

-- CreateIndex
CREATE INDEX "Tag_category_idx" ON "public"."Tag"("category");

-- CreateIndex
CREATE INDEX "Tag_usageCount_idx" ON "public"."Tag"("usageCount");

-- CreateIndex
CREATE INDEX "User_createdAt_idx" ON "public"."User"("createdAt");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "public"."User"("email");

-- CreateIndex
CREATE INDEX "User_lastVoted_idx" ON "public"."User"("lastVoted");

-- CreateIndex
CREATE INDEX "User_locale_idx" ON "public"."User"("locale");

-- CreateIndex
CREATE INDEX "User_reputation_idx" ON "public"."User"("reputation");

-- CreateIndex
CREATE INDEX "User_voteCount_idx" ON "public"."User"("voteCount");

-- CreateIndex
CREATE INDEX "UserAchievement_achievementId_idx" ON "public"."UserAchievement"("achievementId");

-- CreateIndex
CREATE INDEX "UserAchievement_userId_idx" ON "public"."UserAchievement"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserAchievement_userId_achievementId_key" ON "public"."UserAchievement"("userId", "achievementId");

-- CreateIndex
CREATE INDEX "Blacklist_createdAt_idx" ON "public"."Blacklist"("createdAt");

-- CreateIndex
CREATE INDEX "Blacklist_expiresAt_idx" ON "public"."Blacklist"("expiresAt");

-- CreateIndex
CREATE INDEX "Blacklist_userId_expiresAt_idx" ON "public"."Blacklist"("userId", "expiresAt");

-- CreateIndex
CREATE INDEX "Blacklist_userId_idx" ON "public"."Blacklist"("userId");

-- CreateIndex
CREATE INDEX "Blacklist_userId_type_idx" ON "public"."Blacklist"("userId", "type");

-- CreateIndex
CREATE INDEX "LeaderboardEntry_hubId_type_period_idx" ON "public"."LeaderboardEntry"("hubId", "type", "period");

-- CreateIndex
CREATE INDEX "LeaderboardEntry_lastActivityAt_idx" ON "public"."LeaderboardEntry"("lastActivityAt");

-- CreateIndex
CREATE INDEX "LeaderboardEntry_serverId_type_period_idx" ON "public"."LeaderboardEntry"("serverId", "type", "period");

-- CreateIndex
CREATE INDEX "LeaderboardEntry_type_period_score_idx" ON "public"."LeaderboardEntry"("type", "period", "score");

-- CreateIndex
CREATE INDEX "LeaderboardEntry_userId_type_period_idx" ON "public"."LeaderboardEntry"("userId", "type", "period");

-- CreateIndex
CREATE INDEX "ServerBlacklist_createdAt_idx" ON "public"."ServerBlacklist"("createdAt");

-- CreateIndex
CREATE INDEX "ServerBlacklist_expiresAt_idx" ON "public"."ServerBlacklist"("expiresAt");

-- CreateIndex
CREATE INDEX "ServerBlacklist_serverId_expiresAt_idx" ON "public"."ServerBlacklist"("serverId", "expiresAt");

-- CreateIndex
CREATE INDEX "ServerBlacklist_serverId_idx" ON "public"."ServerBlacklist"("serverId");

-- CreateIndex
CREATE INDEX "ServerBlacklist_serverId_type_idx" ON "public"."ServerBlacklist"("serverId", "type");

-- CreateIndex
CREATE INDEX "GlobalReport_createdAt_idx" ON "public"."GlobalReport"("createdAt");

-- CreateIndex
CREATE INDEX "GlobalReport_handledBy_idx" ON "public"."GlobalReport"("handledBy");

-- CreateIndex
CREATE INDEX "GlobalReport_messageId_idx" ON "public"."GlobalReport"("messageId");

-- CreateIndex
CREATE INDEX "GlobalReport_reportedUserId_idx" ON "public"."GlobalReport"("reportedUserId");

-- CreateIndex
CREATE INDEX "GlobalReport_reporterId_idx" ON "public"."GlobalReport"("reporterId");

-- CreateIndex
CREATE INDEX "GlobalReport_status_idx" ON "public"."GlobalReport"("status");

-- CreateIndex
CREATE INDEX "HubReport_createdAt_idx" ON "public"."HubReport"("createdAt");

-- CreateIndex
CREATE INDEX "HubReport_handledBy_idx" ON "public"."HubReport"("handledBy");

-- CreateIndex
CREATE INDEX "HubReport_hubId_idx" ON "public"."HubReport"("hubId");

-- CreateIndex
CREATE INDEX "HubReport_messageId_idx" ON "public"."HubReport"("messageId");

-- CreateIndex
CREATE INDEX "HubReport_reportedUserId_idx" ON "public"."HubReport"("reportedUserId");

-- CreateIndex
CREATE INDEX "HubReport_reporterId_idx" ON "public"."HubReport"("reporterId");

-- CreateIndex
CREATE INDEX "HubReport_status_idx" ON "public"."HubReport"("status");

-- CreateIndex
CREATE UNIQUE INDEX "HubMessageReaction_messageId_emoji_key" ON "public"."HubMessageReaction"("messageId", "emoji");

-- CreateIndex
CREATE INDEX "AntiSwearWhitelist_ruleId_idx" ON "public"."AntiSwearWhitelist"("ruleId");

-- CreateIndex
CREATE INDEX "AntiSwearWhitelist_word_idx" ON "public"."AntiSwearWhitelist"("word");

-- CreateIndex
CREATE UNIQUE INDEX "AntiSwearWhitelist_ruleId_word_key" ON "public"."AntiSwearWhitelist"("ruleId", "word");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "public"."Session"("sessionToken");

-- CreateIndex
CREATE INDEX "_HubToTag_B_index" ON "public"."_HubToTag"("B");

-- AddForeignKey
ALTER TABLE "public"."AntiSwearPattern" ADD CONSTRAINT "AntiSwearPattern_ruleId_fkey" FOREIGN KEY ("ruleId") REFERENCES "public"."AntiSwearRule"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."AntiSwearRule" ADD CONSTRAINT "AntiSwearRule_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."AntiSwearRule" ADD CONSTRAINT "AntiSwearRule_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Appeal" ADD CONSTRAINT "Appeal_infractionId_fkey" FOREIGN KEY ("infractionId") REFERENCES "public"."Infraction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Appeal" ADD CONSTRAINT "Appeal_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."BlockWord" ADD CONSTRAINT "BlockWord_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."BlockWord" ADD CONSTRAINT "BlockWord_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Broadcast" ADD CONSTRAINT "Broadcast_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "public"."Message"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Connection" ADD CONSTRAINT "Connection_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Connection" ADD CONSTRAINT "Connection_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES "public"."ServerData"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Hub" ADD CONSTRAINT "Hub_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubActivityMetrics" ADD CONSTRAINT "HubActivityMetrics_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubInvite" ADD CONSTRAINT "HubInvite_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubLogConfig" ADD CONSTRAINT "HubLogConfig_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubModerator" ADD CONSTRAINT "HubModerator_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubModerator" ADD CONSTRAINT "HubModerator_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReview" ADD CONSTRAINT "HubReview_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReview" ADD CONSTRAINT "HubReview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubRulesAcceptance" ADD CONSTRAINT "HubRulesAcceptance_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubRulesAcceptance" ADD CONSTRAINT "HubRulesAcceptance_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubUpvote" ADD CONSTRAINT "HubUpvote_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubUpvote" ADD CONSTRAINT "HubUpvote_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Infraction" ADD CONSTRAINT "Infraction_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Infraction" ADD CONSTRAINT "Infraction_moderatorId_fkey" FOREIGN KEY ("moderatorId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Infraction" ADD CONSTRAINT "Infraction_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES "public"."ServerData"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Infraction" ADD CONSTRAINT "Infraction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Message" ADD CONSTRAINT "Message_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Message" ADD CONSTRAINT "Message_referredMessageId_fkey" FOREIGN KEY ("referredMessageId") REFERENCES "public"."Message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."ReputationLog" ADD CONSTRAINT "ReputationLog_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."UserAchievement" ADD CONSTRAINT "UserAchievement_achievementId_fkey" FOREIGN KEY ("achievementId") REFERENCES "public"."Achievement"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."UserAchievement" ADD CONSTRAINT "UserAchievement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."UserAchievementProgress" ADD CONSTRAINT "UserAchievementProgress_achievementId_fkey" FOREIGN KEY ("achievementId") REFERENCES "public"."Achievement"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."UserAchievementProgress" ADD CONSTRAINT "UserAchievementProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Blacklist" ADD CONSTRAINT "Blacklist_moderatorId_fkey" FOREIGN KEY ("moderatorId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Blacklist" ADD CONSTRAINT "Blacklist_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."LeaderboardEntry" ADD CONSTRAINT "LeaderboardEntry_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."LeaderboardEntry" ADD CONSTRAINT "LeaderboardEntry_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES "public"."ServerData"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."LeaderboardEntry" ADD CONSTRAINT "LeaderboardEntry_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."ServerBlacklist" ADD CONSTRAINT "ServerBlacklist_moderatorId_fkey" FOREIGN KEY ("moderatorId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."ServerBlacklist" ADD CONSTRAINT "ServerBlacklist_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES "public"."ServerData"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubAnnouncement" ADD CONSTRAINT "HubAnnouncement_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."GlobalReport" ADD CONSTRAINT "GlobalReport_handledBy_fkey" FOREIGN KEY ("handledBy") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."GlobalReport" ADD CONSTRAINT "GlobalReport_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "public"."Message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."GlobalReport" ADD CONSTRAINT "GlobalReport_reportedUserId_fkey" FOREIGN KEY ("reportedUserId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."GlobalReport" ADD CONSTRAINT "GlobalReport_reporterId_fkey" FOREIGN KEY ("reporterId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReport" ADD CONSTRAINT "HubReport_handledBy_fkey" FOREIGN KEY ("handledBy") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReport" ADD CONSTRAINT "HubReport_hubId_fkey" FOREIGN KEY ("hubId") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReport" ADD CONSTRAINT "HubReport_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "public"."Message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReport" ADD CONSTRAINT "HubReport_reportedUserId_fkey" FOREIGN KEY ("reportedUserId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubReport" ADD CONSTRAINT "HubReport_reporterId_fkey" FOREIGN KEY ("reporterId") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."HubMessageReaction" ADD CONSTRAINT "HubMessageReaction_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "public"."Message"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."AntiSwearWhitelist" ADD CONSTRAINT "AntiSwearWhitelist_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."AntiSwearWhitelist" ADD CONSTRAINT "AntiSwearWhitelist_ruleId_fkey" FOREIGN KEY ("ruleId") REFERENCES "public"."AntiSwearRule"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_HubToTag" ADD CONSTRAINT "_HubToTag_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Hub"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_HubToTag" ADD CONSTRAINT "_HubToTag_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Tag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

