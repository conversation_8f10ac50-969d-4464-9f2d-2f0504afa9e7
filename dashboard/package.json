{"name": "my-app", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "lint": "biome check", "format": "biome format --write", "typecheck": "tsc --build --noEmit && echo \"Success!\"", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@discordjs/rest": "^2.6.0", "@prisma/client": "^6.15.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@sentry/nextjs": "^10", "@tanstack/react-query": "^5.85.9", "@tanstack/react-virtual": "^3.13.12", "@trpc/client": "^11.5.0", "@trpc/next": "^11.5.0", "@trpc/server": "^11.5.0", "@trpc/tanstack-react-query": "^11.5.0", "@uploadthing/react": "^7.3.3", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "critters": "^0.0.25", "date-fns": "^4.1.0", "discord-api-types": "^0.38.22", "ioredis": "^5.7.0", "lodash-es": "^4.17.21", "lucide-react": "0.542.0", "motion": "^12.23.12", "next": "^15.5.2", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "uploadthing": "^7.7.4", "zod": "^4.1.5"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@next/bundle-analyzer": "^15.5.2", "@tailwindcss/postcss": "^4.1.12", "@types/canvas-confetti": "^1.9.0", "@types/ioredis": "^5.0.0", "@types/lodash-es": "^4.17.12", "@types/mdx": "^2.0.13", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@types/react-window": "^1.8.8", "esbuild": "^0.25.9", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prisma": "^6.15.0", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}, "trustedDependencies": ["@prisma/client", "@prisma/engines", "@sentry/cli", "@tailwindcss/oxide", "esbuild", "msgpackr-extract", "prisma", "sharp", "unrs-resolver"]}