// Service Worker for InterChat Dashboard
// Handles caching of external images and critical resources

const CACHE_NAME = 'interchat-dashboard-v1';
const STATIC_CACHE_NAME = 'interchat-static-v1';
const IMAGE_CACHE_NAME = 'interchat-images-v1';

// Resources to cache immediately
const STATIC_RESOURCES = [
  '/',
  '/discover',
  '/InterChatLogo.svg',
  '/default-server.svg',
  '/default-avatar.svg',
  '/default-banner.svg'
];

// External domains to cache images from
const EXTERNAL_IMAGE_DOMAINS = [
  'cdn.discordapp.com',
  'media.discordapp.net',
  'i.imgur.com',
  'utfs.io',
  'api.dicebear.com'
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Caching static resources');
        return cache.addAll(STATIC_RESOURCES);
      })
      .then(() => {
        // Skip waiting to activate immediately
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== STATIC_CACHE_NAME && 
                cacheName !== IMAGE_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // Take control of all clients immediately
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isStaticResource(request)) {
    event.respondWith(handleStaticRequest(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else {
    // Default network-first strategy
    event.respondWith(
      fetch(request).catch(() => {
        return caches.match(request);
      })
    );
  }
});

// Check if request is for an image
function isImageRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname.toLowerCase();
  
  return (
    request.destination === 'image' ||
    pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/i) ||
    EXTERNAL_IMAGE_DOMAINS.some(domain => url.hostname.includes(domain))
  );
}

// Check if request is for a static resource
function isStaticResource(request) {
  const url = new URL(request.url);
  return (
    url.pathname.startsWith('/_next/static/') ||
    url.pathname.startsWith('/static/') ||
    STATIC_RESOURCES.includes(url.pathname)
  );
}

// Check if request is for API
function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/');
}

// Handle image requests with cache-first strategy and timeout
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(request, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      // Cache successful responses
      cache.put(request, response.clone());
      return response;
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    console.warn('Image fetch failed:', request.url, error);
    
    // Return fallback image
    const fallbackUrl = getFallbackImage(request.url);
    if (fallbackUrl) {
      const fallbackResponse = await cache.match(fallbackUrl);
      if (fallbackResponse) {
        return fallbackResponse;
      }
    }
    
    // Return a transparent 1x1 pixel as last resort
    return new Response(
      new Uint8Array([
        0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00,
        0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x21,
        0xF9, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x00, 0x00,
        0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x04,
        0x01, 0x00, 0x3B
      ]),
      {
        headers: {
          'Content-Type': 'image/gif',
          'Cache-Control': 'max-age=31536000'
        }
      }
    );
  }
}

// Handle static resources with cache-first strategy
async function handleStaticRequest(request) {
  const cache = await caches.open(STATIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const response = await fetch(request);
    if (response.ok) {
      cache.put(request, response.clone());
    }
    return response;
  } catch (error) {
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
  try {
    const response = await fetch(request);
    
    // Cache successful GET requests
    if (response.ok && request.method === 'GET') {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    // Try to serve from cache for GET requests
    if (request.method === 'GET') {
      const cache = await caches.open(CACHE_NAME);
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }
    
    throw error;
  }
}

// Get appropriate fallback image based on URL
function getFallbackImage(url) {
  const urlLower = url.toLowerCase();
  
  if (urlLower.includes('avatar') || urlLower.includes('profile')) {
    return '/default-avatar.svg';
  }
  if (urlLower.includes('banner')) {
    return '/default-banner.svg';
  }
  if (urlLower.includes('server') || urlLower.includes('guild')) {
    return '/default-server.svg';
  }
  
  return '/InterChatLogo.svg';
}

// Handle background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Retry failed requests when back online
  console.log('Background sync triggered');
}

// Handle push notifications (if needed in future)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/InterChatLogo.svg',
        badge: '/InterChatLogo.svg'
      })
    );
  }
});

// Clean up old cache entries periodically
setInterval(() => {
  caches.open(IMAGE_CACHE_NAME).then(cache => {
    cache.keys().then(keys => {
      if (keys.length > 100) {
        // Remove oldest entries
        const oldestKeys = keys.slice(0, 20);
        oldestKeys.forEach(key => cache.delete(key));
      }
    });
  });
}, 60000); // Run every minute
