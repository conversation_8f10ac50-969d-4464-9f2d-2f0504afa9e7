#!/usr/bin/env node

/**
 * Performance Testing Script for InterChat Dashboard
 * Tests Core Web Vitals and loading performance
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  pages: [
    '/',
    '/discover',
    '/dashboard'
  ],
  iterations: 3,
  timeout: 30000,
  viewport: {
    width: 1920,
    height: 1080
  },
  mobileViewport: {
    width: 375,
    height: 667
  }
};

// Core Web Vitals thresholds
const THRESHOLDS = {
  FCP: 1800, // First Contentful Paint (ms)
  LCP: 2500, // Largest Contentful Paint (ms)
  FID: 100,  // First Input Delay (ms)
  CLS: 0.1,  // Cumulative Layout Shift
  TTFB: 600  // Time to First Byte (ms)
};

class PerformanceTester {
  constructor() {
    this.results = [];
    this.browser = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
  }

  async testPage(url, viewport = CONFIG.viewport, deviceType = 'desktop') {
    const page = await this.browser.newPage();
    
    try {
      await page.setViewport(viewport);
      
      // Enable performance monitoring
      await page.evaluateOnNewDocument(() => {
        window.performanceMetrics = {
          navigationStart: performance.now(),
          metrics: {}
        };
      });

      // Start performance measurement
      const startTime = Date.now();
      
      // Navigate to page
      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: CONFIG.timeout
      });

      const loadTime = Date.now() - startTime;

      // Collect Core Web Vitals
      const metrics = await page.evaluate(() => {
        return new Promise((resolve) => {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const metrics = {};

            entries.forEach((entry) => {
              if (entry.entryType === 'navigation') {
                metrics.TTFB = entry.responseStart - entry.fetchStart;
                metrics.domContentLoaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
                metrics.loadComplete = entry.loadEventEnd - entry.loadEventStart;
              }
              
              if (entry.entryType === 'paint') {
                if (entry.name === 'first-contentful-paint') {
                  metrics.FCP = entry.startTime;
                }
              }
              
              if (entry.entryType === 'largest-contentful-paint') {
                metrics.LCP = entry.startTime;
              }
              
              if (entry.entryType === 'first-input') {
                metrics.FID = entry.processingStart - entry.startTime;
              }
            });

            resolve(metrics);
          });

          try {
            observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input'] });
            
            // Fallback timeout
            setTimeout(() => resolve({}), 5000);
          } catch (e) {
            resolve({});
          }
        });
      });

      // Measure CLS
      const cls = await page.evaluate(() => {
        return new Promise((resolve) => {
          let clsValue = 0;
          
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
          });

          try {
            observer.observe({ entryTypes: ['layout-shift'] });
            setTimeout(() => {
              observer.disconnect();
              resolve(clsValue);
            }, 3000);
          } catch (e) {
            resolve(0);
          }
        });
      });

      // Get resource loading stats
      const resourceStats = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        const stats = {
          total: resources.length,
          images: 0,
          scripts: 0,
          stylesheets: 0,
          failed: 0,
          slow: 0
        };

        resources.forEach(resource => {
          if (resource.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
            stats.images++;
          } else if (resource.name.match(/\.js$/i)) {
            stats.scripts++;
          } else if (resource.name.match(/\.css$/i)) {
            stats.stylesheets++;
          }

          if (resource.duration > 3000) {
            stats.slow++;
          }

          // Check for failed resources (status 0 or 4xx/5xx)
          if (resource.transferSize === 0 && resource.decodedBodySize === 0) {
            stats.failed++;
          }
        });

        return stats;
      });

      const result = {
        url,
        deviceType,
        timestamp: new Date().toISOString(),
        loadTime,
        statusCode: response.status(),
        metrics: {
          ...metrics,
          CLS: cls
        },
        resourceStats,
        passed: this.checkThresholds(metrics, cls),
        viewport
      };

      console.log(`✓ Tested ${url} (${deviceType}): ${loadTime}ms`);
      return result;

    } catch (error) {
      console.error(`✗ Failed to test ${url} (${deviceType}):`, error.message);
      return {
        url,
        deviceType,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    } finally {
      await page.close();
    }
  }

  checkThresholds(metrics, cls) {
    const checks = {
      FCP: (metrics.FCP || 0) <= THRESHOLDS.FCP,
      LCP: (metrics.LCP || 0) <= THRESHOLDS.LCP,
      FID: (metrics.FID || 0) <= THRESHOLDS.FID,
      CLS: cls <= THRESHOLDS.CLS,
      TTFB: (metrics.TTFB || 0) <= THRESHOLDS.TTFB
    };

    return {
      ...checks,
      overall: Object.values(checks).every(Boolean)
    };
  }

  async runTests() {
    console.log('🚀 Starting performance tests...\n');

    for (const pagePath of CONFIG.pages) {
      const url = `${CONFIG.baseUrl}${pagePath}`;
      
      console.log(`Testing: ${url}`);
      
      // Test desktop
      for (let i = 0; i < CONFIG.iterations; i++) {
        const result = await this.testPage(url, CONFIG.viewport, 'desktop');
        this.results.push(result);
      }
      
      // Test mobile
      for (let i = 0; i < CONFIG.iterations; i++) {
        const result = await this.testPage(url, CONFIG.mobileViewport, 'mobile');
        this.results.push(result);
      }
      
      console.log('');
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      config: CONFIG,
      thresholds: THRESHOLDS,
      summary: this.generateSummary(),
      results: this.results
    };

    // Save detailed report
    const reportPath = path.join(__dirname, '../performance-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // Generate human-readable summary
    this.printSummary(report.summary);
    
    console.log(`\n📊 Detailed report saved to: ${reportPath}`);
    
    return report;
  }

  generateSummary() {
    const summary = {
      totalTests: this.results.length,
      passed: 0,
      failed: 0,
      byPage: {},
      averages: {}
    };

    this.results.forEach(result => {
      if (result.error) {
        summary.failed++;
        return;
      }

      if (result.passed?.overall) {
        summary.passed++;
      } else {
        summary.failed++;
      }

      // Group by page
      if (!summary.byPage[result.url]) {
        summary.byPage[result.url] = {
          desktop: { tests: 0, passed: 0, metrics: [] },
          mobile: { tests: 0, passed: 0, metrics: [] }
        };
      }

      const pageData = summary.byPage[result.url][result.deviceType];
      pageData.tests++;
      if (result.passed?.overall) pageData.passed++;
      pageData.metrics.push(result.metrics);
    });

    // Calculate averages
    Object.keys(summary.byPage).forEach(url => {
      ['desktop', 'mobile'].forEach(device => {
        const data = summary.byPage[url][device];
        if (data.metrics.length > 0) {
          data.averages = {
            FCP: this.average(data.metrics, 'FCP'),
            LCP: this.average(data.metrics, 'LCP'),
            CLS: this.average(data.metrics, 'CLS'),
            TTFB: this.average(data.metrics, 'TTFB')
          };
        }
      });
    });

    return summary;
  }

  average(metrics, key) {
    const values = metrics.map(m => m[key] || 0).filter(v => v > 0);
    return values.length > 0 ? Math.round(values.reduce((a, b) => a + b, 0) / values.length) : 0;
  }

  printSummary(summary) {
    console.log('\n📈 PERFORMANCE TEST RESULTS');
    console.log('=' .repeat(50));
    console.log(`Total Tests: ${summary.totalTests}`);
    console.log(`Passed: ${summary.passed} (${Math.round(summary.passed / summary.totalTests * 100)}%)`);
    console.log(`Failed: ${summary.failed} (${Math.round(summary.failed / summary.totalTests * 100)}%)`);
    
    console.log('\n📊 Results by Page:');
    Object.entries(summary.byPage).forEach(([url, data]) => {
      console.log(`\n${url}:`);
      ['desktop', 'mobile'].forEach(device => {
        const deviceData = data[device];
        if (deviceData.tests > 0) {
          console.log(`  ${device.toUpperCase()}:`);
          console.log(`    Tests: ${deviceData.tests}, Passed: ${deviceData.passed}`);
          if (deviceData.averages) {
            console.log(`    Avg FCP: ${deviceData.averages.FCP}ms (target: <${THRESHOLDS.FCP}ms)`);
            console.log(`    Avg LCP: ${deviceData.averages.LCP}ms (target: <${THRESHOLDS.LCP}ms)`);
            console.log(`    Avg CLS: ${deviceData.averages.CLS.toFixed(3)} (target: <${THRESHOLDS.CLS})`);
            console.log(`    Avg TTFB: ${deviceData.averages.TTFB}ms (target: <${THRESHOLDS.TTFB}ms)`);
          }
        }
      });
    });
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Main execution
async function main() {
  const tester = new PerformanceTester();
  
  try {
    await tester.init();
    await tester.runTests();
    const report = tester.generateReport();
    
    // Exit with error code if tests failed
    process.exit(report.summary.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('❌ Performance testing failed:', error);
    process.exit(1);
  } finally {
    await tester.cleanup();
  }
}

if (require.main === module) {
  main();
}

module.exports = PerformanceTester;
