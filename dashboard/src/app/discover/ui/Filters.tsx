'use client';

import { useMemo } from 'react';
import { TagPicker } from '@/components/discover/TagPicker';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SUPPORTED_LANGUAGES } from '@/lib/constants';

export type FeatureFlags = {
  verified?: boolean;
  partnered?: boolean;
  nsfw?: boolean;
};

export function Filters(props: {
  q: string;
  onQChange: (v: string) => void;
  sort: 'trending' | 'active' | 'new' | 'upvoted';
  onSortChange: (v: 'trending' | 'active' | 'new' | 'upvoted') => void;

  tags: string[];
  onTagsChange: (tags: string[]) => void;

  language?: string;
  onLanguageChange: (lang?: string) => void;

  region?: string;
  onRegionChange: (region?: string) => void;

  activity: ('LOW' | 'MEDIUM' | 'HIGH')[];
  onActivityChange: (values: ('LOW' | 'MEDIUM' | 'HIGH')[]) => void;

  features: FeatureFlags;
  onFeaturesChange: (f: FeatureFlags) => void;
}) {
  const languageOptions = useMemo(() => SUPPORTED_LANGUAGES, []);

  return (
    <aside className="premium-card-enhanced space-y-6 rounded-[var(--radius)] border-gray-600/50 p-6 shadow-2xl backdrop-blur-md">
      <div className="flex items-center gap-2 border-gray-700/30 border-b pb-2">
        <h2 className="font-semibold text-sm text-white">Filters</h2>
      </div>

      <div className="flex flex-col gap-6">
        {/* Sort */}
        <div className="flex flex-col gap-3">
          <Label className="font-medium text-gray-300 text-xs uppercase tracking-wide">
            Sort by
          </Label>
          <Select
            value={props.sort}
            onValueChange={(v: string) =>
              props.onSortChange(v as 'trending' | 'active' | 'new' | 'upvoted')
            }
          >
            <SelectTrigger className="select-standard">
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent className="select-content">
              <SelectItem value="trending">🔥 Trending</SelectItem>
              <SelectItem value="active">⚡ Most Active</SelectItem>
              <SelectItem value="new">✨ Newest</SelectItem>
              <SelectItem value="upvoted">👍 Most Upvoted</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <TagPicker
          selectedTags={props.tags}
          onTagsChange={props.onTagsChange}
          maxTags={5}
        />

        {/* Language */}
        <div className="flex flex-col gap-3">
          <Label className="font-medium text-gray-300 text-xs uppercase tracking-wide">
            Language
          </Label>
          <Select
            value={props.language ?? 'any'}
            onValueChange={(v) =>
              props.onLanguageChange(v === 'any' ? undefined : v)
            }
          >
            <SelectTrigger className="select-standard">
              <SelectValue placeholder="Any language" />
            </SelectTrigger>
            <SelectContent className="select-content">
              <SelectItem value="any">Any language</SelectItem>
              {languageOptions.map((l) => (
                <SelectItem key={l.code} value={l.code}>
                  {l.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {/* Activity */}
        <div className="flex flex-col gap-3">
          <Label className="font-medium text-gray-300 text-xs uppercase tracking-wide">
            Activity Level
          </Label>
          <div className="grid grid-cols-1 gap-2">
            {(['LOW', 'MEDIUM', 'HIGH'] as const).map((lvl) => {
              const checked = props.activity.includes(lvl);
              const activityIcons = { LOW: '🐢', MEDIUM: '🚶', HIGH: '🚀' };
              const activityColors = {
                LOW: 'text-amber-300 border-amber-500/30 bg-amber-500/10',
                MEDIUM: 'text-blue-300 border-blue-500/30 bg-blue-500/10',
                HIGH: 'text-emerald-300 border-emerald-500/30 bg-emerald-500/10',
              };
              return (
                <Label
                  key={lvl}
                  className={`inline-flex cursor-pointer items-center gap-3 rounded-[var(--radius-button)] border p-3 transition-all duration-200 hover:bg-gray-800/50 ${
                    checked
                      ? activityColors[lvl]
                      : 'border-gray-700/50 bg-gray-900/30 text-gray-300 hover:border-gray-600/50'
                  }`}
                >
                  <Checkbox
                    checked={checked}
                    onCheckedChange={(val) => {
                      const on = Boolean(val);
                      let next = props.activity;
                      if (on && !next.includes(lvl)) next = [...next, lvl];
                      if (!on) next = next.filter((x) => x !== lvl);
                      props.onActivityChange(next);
                    }}
                    className="data-[state=checked]:border-current data-[state=checked]:bg-current"
                  />
                  <span className="text-sm">{activityIcons[lvl]}</span>
                  <span className="font-medium text-sm capitalize">
                    {lvl.toLowerCase()}
                  </span>
                </Label>
              );
            })}
          </div>
        </div>

        {/* Features */}
        <div className="flex flex-col gap-3">
          <Label className="font-medium text-gray-300 text-xs uppercase tracking-wide">
            Features
          </Label>
          <div className="grid grid-cols-1 gap-2">
            <Label className="inline-flex cursor-pointer items-center gap-3 rounded-[var(--radius-button)] border border-gray-700/50 bg-gray-900/30 p-3 transition-all duration-200 hover:bg-gray-800/50">
              <Checkbox
                checked={!!props.features.verified}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({
                    ...props.features,
                    verified: Boolean(v),
                  })
                }
                className="data-[state=checked]:border-emerald-500 data-[state=checked]:bg-emerald-500"
              />
              <span className="text-sm">✅</span>
              <span className="font-medium text-gray-200 text-sm">
                Verified
              </span>
            </Label>
            <Label className="inline-flex cursor-pointer items-center gap-3 rounded-[var(--radius-button)] border border-gray-700/50 bg-gray-900/30 p-3 transition-all duration-200 hover:bg-gray-800/50">
              <Checkbox
                checked={!!props.features.partnered}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({
                    ...props.features,
                    partnered: Boolean(v),
                  })
                }
                className="data-[state=checked]:border-indigo-500 data-[state=checked]:bg-indigo-500"
              />
              <span className="text-sm">🤝</span>
              <span className="font-medium text-gray-200 text-sm">
                Partnered
              </span>
            </Label>
            <Label className="inline-flex cursor-pointer items-center gap-3 rounded-[var(--radius-button)] border border-gray-700/50 bg-gray-900/30 p-3 transition-all duration-200 hover:bg-gray-800/50">
              <Checkbox
                checked={!!props.features.nsfw}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({
                    ...props.features,
                    nsfw: Boolean(v),
                  })
                }
                className="data-[state=checked]:border-red-500 data-[state=checked]:bg-red-500"
              />
              <span className="text-sm">🔞</span>
              <span className="font-medium text-gray-200 text-sm">NSFW</span>
            </Label>
          </div>
        </div>
      </div>
    </aside>
  );
}
