'use server';

import { Info, MessageSquare, ScrollText, Settings, Users } from 'lucide-react';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { auth } from '@/auth';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PermissionLevel } from '@/lib/constants';
import { getHubConnections, getHubData } from '@/lib/hub-queries';
import { getUserHubPermission } from '@/lib/permissions';
import { cn } from '@/lib/utils';
import ClientReviewSection from '../components/hub-detail/ClientReviewSection';
import HubBanner from '../components/hub-detail/HubBanner';
import HubConnectedServers from '../components/hub-detail/HubConnectedServers';
import HubDetailsCard from '../components/hub-detail/HubDetailsCard';
import HubInfoCard from '../components/hub-detail/HubInfoCard';
import HubModeratorsCard from '../components/hub-detail/HubModeratorsCard';
import HubOverview from '../components/hub-detail/HubOverview';
import HubReviewAnalytics from '../components/hub-detail/HubReviewAnalytics';
import HubRules from '../components/hub-detail/HubRules';
import JoinButton from '../components/hub-detail/JoinButton';
import ReviewItem from '../components/hub-detail/ReviewItem';
import SimilarHubsCard from '../components/hub-detail/SimilarHubsCard';
import UpvoteButton from '../components/hub-detail/UpvoteButton';

// Metadata function remains the same
export async function generateMetadata(props: {
  params: Promise<{ hubId: string }>;
}): Promise<Metadata> {
  const { hubId } = await props.params;
  const hub = await getHubData(hubId);

  if (!hub) return { title: 'Hub Not Found' };

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://interchat.tech';

  return {
    title: `${hub.name} | InterChat Discord Community Hub`,
    description:
      hub.description ||
      'Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.',
    keywords: [
      'discord community',
      'discord hub',
      'connect discord servers',
      'interchat hub',
      ...(hub.tags.map((t) => t.name) || []),
    ],
    openGraph: {
      title: `${hub.name} | InterChat Discord Community Hub`,
      description:
        hub.description ||
        'Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.',
      type: 'website',
      url: `${baseUrl}/hubs/${hubId}`,
      images: [
        {
          url: hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
          width: 1200,
          height: 630,
          alt: hub.name,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      images: [hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`],
      title: `${hub.name} | InterChat Discord Community Hub`,
      description:
        hub.description ||
        'Join this active Discord community hub with InterChat.',
      creator: '@737_dev',
      site: '@interchatapp',
    },
    alternates: {
      canonical: `${baseUrl}/hubs/${hubId}`,
    },
  };
}

export default async function HubDetailView(props: {
  params: Promise<{ hubId: string }>;
}) {
  const { hubId } = await props.params;

  // Check user authentication and permissions
const session = await auth()
  let userPermissionLevel = PermissionLevel.NONE;

  if (session?.user) {
    userPermissionLevel = await getUserHubPermission(session.user.id, hubId);
  }

  const canManageHub = userPermissionLevel >= PermissionLevel.MODERATOR;

  const hub = await getHubData(hubId);
  const connections = (await getHubConnections(hubId)) ?? [];

  if (!hub) {
    notFound();
  }

  const formattedDate = new Date(hub.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Group reviews by rating to show analytics
  const reviewStats = {
    total: hub.reviews?.length || 0,
    average:
      hub.reviews?.reduce((acc, review) => acc + review.rating, 0) /
      (hub.reviews?.length || 1),
    distribution: [5, 4, 3, 2, 1].map((rating) => ({
      rating,
      count: hub.reviews?.filter((r) => r.rating === rating).length || 0,
      percentage: Math.round(
        ((hub.reviews?.filter((r) => r.rating === rating).length || 0) /
          (hub.reviews?.length || 1)) *
          100
      ),
    })),
  };

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-950 to-gray-900 text-gray-200">
      {/* Main Content */}
      <div className="flex-grow">
        {/* Enhanced Banner Section with taller height and parallax effect */}
        <HubBanner bannerUrl={hub.bannerUrl} name={hub.name} />

        {/* Page Content */}
        <div className="container mx-auto max-w-7xl px-4 pb-16">
          {/* Enhanced Header Card */}
          <div className="-mt-32 md:-mt-40 relative mb-12 transform transition-all duration-300">
            <div className="rounded-2xl border border-gray-800/70 bg-gray-900/90 p-6 shadow-xl backdrop-blur-xl md:p-8">
              <div className="flex w-full flex-col md:flex-row md:items-start md:justify-between">
                <HubInfoCard hub={hub} />

                {/* Action Buttons with enhanced styling */}
                <div className="mt-6 flex flex-shrink-0 gap-3 rounded-lg border border-gray-700/50 bg-gray-800/40 p-2 md:mt-0 md:ml-auto">
                  {/* Manage Hub button - only show for users with management permissions */}
                  {canManageHub && (
                    <Link href={`/dashboard/hubs/${hubId}`}>
                      <Button
                        className="flex cursor-pointer items-center gap-2 rounded-lg border-0 bg-gradient-to-r from-indigo-600 to-purple-600 px-4 py-2 font-medium text-white shadow-lg transition-all duration-200 hover:scale-105 hover:from-indigo-600/80 hover:to-purple-600/80"
                        size="sm"
                        variant="outline"
                      >
                        <Settings className="h-4 w-4" />
                        Manage Hub
                      </Button>
                    </Link>
                  )}

                  {/* Enhanced join button with animation */}
                  <div className="group relative">
                    <JoinButton hubName={hub.name} hubId={hub.id} />
                  </div>

                  {/* Enhanced upvote button */}
                  <UpvoteButton hubId={hub.id} initialUpvotes={hub.upvotes} />
                </div>
              </div>
            </div>
          </div>

          {/* Main Grid (Tabs/Reviews + Sidebar) with improved layout */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Left Column (Tabs and Reviews) */}
            <div className="space-y-8 lg:col-span-2">
              {/* Enhanced Tabs Section */}
              <Tabs defaultValue="overview" className="w-full">
                {/* Tab Triggers with improved styling */}
                <div className="relative mb-6">
                  <TabsList className="inline-flex h-auto w-full items-center justify-start gap-1 rounded-lg border border-gray-700/40 bg-gray-800/40 p-1.5 px-4 sm:gap-2">
                    {[
                      { value: 'overview', icon: Info, label: 'Overview' },
                      { value: 'rules', icon: ScrollText, label: 'Rules' },
                      {
                        value: 'servers',
                        icon: Users,
                        label: 'Connected Servers',
                      },
                    ].map((tab) => (
                      <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className={cn(
                          'inline-flex flex-shrink-0 cursor-pointer items-center justify-center whitespace-nowrap rounded-md px-2 py-2 font-medium text-xs ring-offset-background transition-all sm:px-3 sm:py-2.5 sm:text-sm md:px-4',
                          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                          'disabled:pointer-events-none disabled:opacity-50',
                          'data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary-alt data-[state=active]:text-white data-[state=active]:shadow-md', // Active state
                          'data-[state=active]:animate-tab-glow', // Animation for active tab
                          'text-gray-300 hover:bg-gray-700/50 hover:text-white' // Inactive state
                        )}
                      >
                        <tab.icon className="mr-1 h-3 w-3 flex-shrink-0 sm:mr-2 sm:h-4 sm:w-4" />
                        <span className="truncate">{tab.label}</span>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {/* Tab Content Panes with improved styling */}
                <TabsContent
                  value="overview"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 shadow-lg backdrop-blur-md md:p-8', // Container with padding
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2' // Standard focus styles
                  )}
                >
                  <HubOverview description={hub.description} />

                  {/* Reviews Section - Now part of Overview */}
                  <div className="mt-8 border-gray-700/50 border-t pt-8">
                    <div className="mb-6 flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg text-white">
                        Reviews
                      </h3>
                    </div>

                    <HubReviewAnalytics reviewStats={reviewStats} />

                    {/* Review Form */}
                    <ClientReviewSection hubId={hub.id} />

                    {/* Review list */}
                    <div className="space-y-4">
                      {hub.reviews?.length > 0 ? (
                        hub.reviews.map((review) => (
                          <ReviewItem
                            key={review.id}
                            review={review}
                            hubId={hub.id}
                          />
                        ))
                      ) : (
                        <div className="flex flex-col items-center justify-center p-10 text-gray-400">
                          <MessageSquare className="mb-3 h-12 w-12 text-gray-500 opacity-50" />
                          <p className="text-center text-gray-400">
                            No reviews yet
                          </p>
                          <p className="mt-1 text-center text-gray-500 text-sm">
                            Be the first to review this hub!
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent
                  value="rules"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 shadow-lg backdrop-blur-md md:p-8',
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                  )}
                >
                  <HubRules rules={hub.rules} />
                </TabsContent>

                <TabsContent
                  value="servers"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 shadow-lg backdrop-blur-md md:p-8',
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                  )}
                >
                  <HubConnectedServers connections={connections} />
                </TabsContent>
              </Tabs>
            </div>
            {/* Right Column (Sidebar) */}
            <div className="space-y-4 sm:space-y-6 lg:col-span-1">
              <HubDetailsCard
                formattedDate={formattedDate}
                hub={hub}
                connections={connections}
              />

              <HubModeratorsCard moderators={hub.moderators} />

              <SimilarHubsCard />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
