import type { Metadata } from 'next';
import { CTA } from '@/app/_components/CTA';
import { FaqSection } from '@/app/_components/FaqSection';
import { FeaturesShowcase } from '@/app/_components/FeaturesShowcase';
import { Hero } from '@/app/_components/Hero';
import { HomePageSchemas } from '@/app/_components/HomePageSchemas';
// import { StatsBar } from '@/components/hubs/StatsBar';
import { getPlatformStats, getStatsBarData } from '@/lib/platform-stats';

export const revalidate = 300;

export const metadata: Metadata = {
  title: 'InterChat v5 – Faster, modern, and redesigned',
  description:
    'Introducing InterChat v5: a complete rewrite with improved performance, modern command UIs, and a redesigned dashboard for cross-server communication.',
  keywords: [
    'Discord bot',
    'cross-server chat',
    'InterChat v5',
    'InterChat',
    'Discord hubs',
    'server bridge',
    'Discord moderation',
  ],
  openGraph: {
    title: 'InterChat v5 – Faster, modern, and redesigned',
    description:
      'A complete rewrite with improved performance, enhanced command UIs, and a modern dashboard.',
    url: 'https://interchat.tech',
    siteName: 'InterChat',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'InterChat v5 – Faster, modern, and redesigned',
    description:
      'A complete rewrite with improved performance, enhanced command UIs, and a modern dashboard.',
  },
};

export default async function HomePage() {
  const platformStats = await getPlatformStats();
  const statsData = platformStats.success
    ? getStatsBarData(platformStats.data)
    : undefined;

  return (
    <>
      <HomePageSchemas />

      <main
        className="flex flex-1 flex-col justify-center"
        itemScope
        itemType="https://schema.org/WebPage"
      >
        <Hero />

        {/* Live platform stats */}
        {/* <StatsBar stats={statsData} /> */}

        <FeaturesShowcase />

        {/* <CreditsSection /> */}
        <FaqSection />
        <CTA />
      </main>
    </>
  );
}
