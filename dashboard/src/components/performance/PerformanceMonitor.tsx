'use client';

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);

  useEffect(() => {
    // Only run in development or when explicitly enabled
    if (process.env.NODE_ENV !== 'development' && !process.env.NEXT_PUBLIC_ENABLE_PERF_MONITOR) {
      return;
    }

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          setMetrics(prev => ({
            ...prev,
            loadTime: navEntry.loadEventEnd - navEntry.loadEventStart,
          } as PerformanceMetrics));
        }
        
        if (entry.entryType === 'paint') {
          const paintEntry = entry as PerformancePaintTiming;
          if (paintEntry.name === 'first-contentful-paint') {
            setMetrics(prev => ({
              ...prev,
              firstContentfulPaint: paintEntry.startTime,
            } as PerformanceMetrics));
          }
        }
        
        if (entry.entryType === 'largest-contentful-paint') {
          const lcpEntry = entry as PerformanceEntry;
          setMetrics(prev => ({
            ...prev,
            largestContentfulPaint: lcpEntry.startTime,
          } as PerformanceMetrics));
        }
        
        if (entry.entryType === 'layout-shift') {
          const clsEntry = entry as any;
          if (!clsEntry.hadRecentInput) {
            setMetrics(prev => ({
              ...prev,
              cumulativeLayoutShift: (prev?.cumulativeLayoutShift || 0) + clsEntry.value,
            } as PerformanceMetrics));
          }
        }
        
        if (entry.entryType === 'first-input') {
          const fidEntry = entry as PerformanceEventTiming;
          setMetrics(prev => ({
            ...prev,
            firstInputDelay: fidEntry.processingStart - fidEntry.startTime,
          } as PerformanceMetrics));
        }
      });
    });

    // Observe different performance entry types
    try {
      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'layout-shift', 'first-input'] });
    } catch (e) {
      console.warn('Performance Observer not fully supported:', e);
    }

    return () => observer.disconnect();
  }, []);

  // Don't render anything in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_ENABLE_PERF_MONITOR) {
    return null;
  }

  if (!metrics) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 rounded-lg bg-black/80 p-3 text-xs text-white backdrop-blur-sm">
      <div className="font-semibold mb-2">Performance Metrics</div>
      <div className="space-y-1">
        {metrics.firstContentfulPaint && (
          <div>FCP: {Math.round(metrics.firstContentfulPaint)}ms</div>
        )}
        {metrics.largestContentfulPaint && (
          <div>LCP: {Math.round(metrics.largestContentfulPaint)}ms</div>
        )}
        {metrics.cumulativeLayoutShift && (
          <div>CLS: {metrics.cumulativeLayoutShift.toFixed(3)}</div>
        )}
        {metrics.firstInputDelay && (
          <div>FID: {Math.round(metrics.firstInputDelay)}ms</div>
        )}
        {metrics.loadTime && (
          <div>Load: {Math.round(metrics.loadTime)}ms</div>
        )}
      </div>
    </div>
  );
}
