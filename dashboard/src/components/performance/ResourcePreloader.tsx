'use client';

import { useEffect } from 'react';

interface PreloadResource {
  href: string;
  as: 'image' | 'font' | 'style' | 'script';
  type?: string;
  crossOrigin?: 'anonymous' | 'use-credentials';
}

const CRITICAL_RESOURCES: PreloadResource[] = [
  // Default fallback images
  { href: '/InterChatLogo.svg', as: 'image' },
  { href: '/default-server.svg', as: 'image' },
  { href: '/default-avatar.svg', as: 'image' },
  { href: '/default-banner.svg', as: 'image' },
];

export function ResourcePreloader() {
  useEffect(() => {
    // Preload critical resources
    const preloadResources = () => {
      CRITICAL_RESOURCES.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        
        if (resource.type) {
          link.type = resource.type;
        }
        
        if (resource.crossOrigin) {
          link.crossOrigin = resource.crossOrigin;
        }
        
        // Add error handling
        link.onerror = () => {
          console.warn(`Failed to preload resource: ${resource.href}`);
        };
        
        document.head.appendChild(link);
      });
    };

    // Preload immediately
    preloadResources();

    // Prefetch common external domains
    const prefetchDomains = [
      'https://cdn.discordapp.com',
      'https://media.discordapp.net',
      'https://i.imgur.com',
      'https://utfs.io'
    ];

    prefetchDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });

    // Set up intersection observer for lazy loading optimization
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      // Observe all images with data-src attribute
      const lazyImages = document.querySelectorAll('img[data-src]');
      lazyImages.forEach(img => imageObserver.observe(img));

      return () => imageObserver.disconnect();
    }
  }, []);

  return null;
}

// Hook for lazy loading images
export function useLazyImage(src: string, threshold = 0.1) {
  useEffect(() => {
    if (!('IntersectionObserver' in window)) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              // Create a new image to preload
              const newImg = new Image();
              newImg.onload = () => {
                img.src = img.dataset.src!;
                img.removeAttribute('data-src');
                img.classList.add('loaded');
              };
              newImg.onerror = () => {
                // Use fallback image
                img.src = '/default-server.svg';
                img.classList.add('error');
              };
              newImg.src = img.dataset.src!;
              observer.unobserve(img);
            }
          }
        });
      },
      { threshold, rootMargin: '50px' }
    );

    return () => observer.disconnect();
  }, [src, threshold]);
}

// Performance optimization for critical images
export function preloadCriticalImages(urls: string[]) {
  if (typeof window === 'undefined') return;

  urls.forEach(url => {
    const img = new Image();
    img.src = url;
    
    // Add to cache
    img.onload = () => {
      console.log(`Preloaded: ${url}`);
    };
    
    img.onerror = () => {
      console.warn(`Failed to preload: ${url}`);
    };
  });
}

// Service Worker registration for caching
export function registerServiceWorker() {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return;
  }

  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Image timeout handler
export function createImageWithTimeout(src: string, timeout = 5000): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    let timeoutId: NodeJS.Timeout;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
    };

    img.onload = () => {
      cleanup();
      resolve(img);
    };

    img.onerror = () => {
      cleanup();
      reject(new Error(`Failed to load image: ${src}`));
    };

    timeoutId = setTimeout(() => {
      cleanup();
      reject(new Error(`Image load timeout: ${src}`));
    }, timeout);

    img.src = src;
  });
}

// Batch image preloader with error handling
export async function preloadImagesWithFallback(
  urls: string[], 
  fallbackUrl: string = '/default-server.svg'
): Promise<Map<string, string>> {
  const results = new Map<string, string>();
  
  const promises = urls.map(async (url) => {
    try {
      await createImageWithTimeout(url, 3000);
      results.set(url, url);
    } catch (error) {
      console.warn(`Failed to preload ${url}, using fallback`);
      results.set(url, fallbackUrl);
    }
  });

  await Promise.allSettled(promises);
  return results;
}
