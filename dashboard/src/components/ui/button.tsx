import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import type * as React from 'react';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "relative inline-flex shrink-0 items-center justify-center gap-2 overflow-hidden whitespace-nowrap font-medium text-sm outline-none transition-all duration-300 focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/25 hover:from-purple-600 hover:to-blue-600 hover:shadow-purple-500/30 hover:shadow-xl active:scale-95',
        destructive:
          'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/25 hover:from-red-600 hover:to-red-700 hover:shadow-red-500/30 hover:shadow-xl active:scale-95',
        outline:
          'border border-white/20 bg-white/5 text-white shadow-lg backdrop-blur-sm hover:border-white/30 hover:bg-white/10 hover:shadow-xl active:scale-95',
        secondary:
          'bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-gray-700/25 shadow-lg hover:from-gray-600 hover:to-gray-700 hover:shadow-gray-700/30 hover:shadow-xl active:scale-95',
        ghost:
          'text-gray-300 hover:bg-white/10 hover:text-white active:scale-95',
        link: 'text-purple-400 underline-offset-4 hover:text-purple-300 hover:underline',
      },
      size: {
        default:
          'h-10 rounded-[var(--radius-button)] px-6 py-2 has-[>svg]:px-5',
        sm: 'h-8 gap-1.5 rounded-[var(--radius-md)] px-4 has-[>svg]:px-3',
        lg: 'h-12 rounded-[var(--radius-button)] px-8 text-base has-[>svg]:px-6',
        icon: 'size-10 rounded-[var(--radius-button)]',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
