'use client';

import Image from 'next/image';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface HydrationSafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  fallbackSrc?: string;
  onError?: () => void;
  onLoad?: () => void;
}

// Default fallback images
const DEFAULT_FALLBACKS = {
  avatar: '/default-avatar.svg',
  banner: '/default-banner.svg',
  server: '/default-server.svg',
  hub: '/InterChatLogo.svg',
} as const;

export function HydrationSafeImage({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  sizes,
  quality = 75,
  fallbackSrc,
  onError,
  onLoad,
  ...props
}: HydrationSafeImageProps) {
  const [error, setError] = useState(false);

  // Determine fallback image based on alt text or use provided fallback
  const determinedFallback = fallbackSrc || (() => {
    const altLower = alt.toLowerCase();
    if (altLower.includes('avatar') || altLower.includes('profile')) {
      return DEFAULT_FALLBACKS.avatar;
    }
    if (altLower.includes('banner')) {
      return DEFAULT_FALLBACKS.banner;
    }
    if (altLower.includes('server')) {
      return DEFAULT_FALLBACKS.server;
    }
    if (altLower.includes('hub') || altLower.includes('community')) {
      return DEFAULT_FALLBACKS.hub;
    }
    return DEFAULT_FALLBACKS.hub;
  })();

  const handleError = () => {
    setError(true);
    onError?.();
  };

  const handleLoad = () => {
    onLoad?.();
  };

  // Use fallback if there's an error
  const imageSrc = error ? determinedFallback : src;

  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      fill={fill}
      className={cn(className, error && 'opacity-60')}
      priority={priority}
      sizes={sizes}
      quality={quality}
      onError={handleError}
      onLoad={handleLoad}
      {...props}
    />
  );
}

// Specialized avatar component with proper circular styling
export function SafeHubAvatar({ 
  src, 
  name, 
  size = 56, 
  className,
  ...props 
}: {
  src: string;
  name: string;
  size?: number;
  className?: string;
} & Omit<HydrationSafeImageProps, 'src' | 'alt' | 'width' | 'height'>) {
  return (
    <div 
      className={cn('relative overflow-hidden rounded-full bg-gray-800', className)} 
      style={{ width: size, height: size }}
    >
      <HydrationSafeImage
        src={src}
        alt={`${name} avatar`}
        width={size}
        height={size}
        className="object-cover"
        fallbackSrc={DEFAULT_FALLBACKS.avatar}
        {...props}
      />
    </div>
  );
}

// Specialized banner component
export function SafeHubBanner({ 
  src, 
  name, 
  className,
  ...props 
}: {
  src: string;
  name: string;
  className?: string;
} & Omit<HydrationSafeImageProps, 'src' | 'alt'>) {
  return (
    <HydrationSafeImage
      src={src}
      alt={`${name} banner`}
      fill
      className={cn('object-cover', className)}
      fallbackSrc={DEFAULT_FALLBACKS.banner}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      {...props}
    />
  );
}

// Specialized server icon component
export function SafeServerIcon({ 
  src, 
  name, 
  size = 48, 
  className,
  ...props 
}: {
  src: string;
  name: string;
  size?: number;
  className?: string;
} & Omit<HydrationSafeImageProps, 'src' | 'alt' | 'width' | 'height'>) {
  return (
    <HydrationSafeImage
      src={src}
      alt={`${name} server icon`}
      width={size}
      height={size}
      className={cn('rounded-lg', className)}
      fallbackSrc={DEFAULT_FALLBACKS.server}
      {...props}
    />
  );
}
