import { Redis } from 'ioredis';

// Redis client for caching
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// In-memory cache for ultra-fast access
const memoryCache = new Map<string, { data: unknown; expiry: number }>();

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  memoryTtl?: number; // Memory cache TTL in seconds (shorter than Redis)
}

export class PerformanceCache {
  private static instance: PerformanceCache;

  static getInstance(): PerformanceCache {
    if (!PerformanceCache.instance) {
      PerformanceCache.instance = new PerformanceCache();
    }
    return PerformanceCache.instance;
  }

  /**
   * Get data from cache with multi-layer strategy:
   * 1. Memory cache (fastest)
   * 2. Redis cache (fast)
   * 3. Database (slowest)
   */
  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryData = memoryCache.get(key);
    if (memoryData && memoryData.expiry > Date.now()) {
      console.log(`Memory cache hit for key: ${key}`);
      return memoryData.data as T;
    }

    // Check Redis cache
    try {
      const redisData = await redis.get(key);
      if (redisData) {
        console.log(`Redis cache hit for key: ${key}`);
        const parsed = JSON.parse(redisData) as T;

        // Store in memory cache for next time
        memoryCache.set(key, {
          data: parsed,
          expiry: Date.now() + 60 * 1000, // 1 minute memory cache
        });

        return parsed;
      }
    } catch (error) {
      console.error('Redis cache error:', error);
    }

    return null;
  }

  /**
   * Set data in both memory and Redis cache
   */
  async set<T>(
    key: string,
    data: T,
    options: CacheOptions = {}
  ): Promise<void> {
    const { ttl = 300, memoryTtl = 60 } = options;

    // Set in memory cache
    memoryCache.set(key, {
      data,
      expiry: Date.now() + memoryTtl * 1000,
    });

    // Set in Redis cache
    try {
      await redis.set(key, JSON.stringify(data), 'EX', ttl);
    } catch (error) {
      console.error('Redis cache set error:', error);
    }
  }

  /**
   * Invalidate cache for a specific key
   */
  async invalidate(key: string): Promise<void> {
    memoryCache.delete(key);
    try {
      await redis.del(key);
    } catch (error) {
      console.error('Redis cache invalidation error:', error);
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    memoryCache.clear();
    try {
      await redis.flushall();
    } catch (error) {
      console.error('Redis cache clear error:', error);
    }
  }

  /**
   * Clean expired memory cache entries
   */
  cleanMemoryCache(): void {
    const now = Date.now();
    for (const [key, value] of memoryCache.entries()) {
      if (value.expiry <= now) {
        memoryCache.delete(key);
      }
    }
  }
}

// Cache keys
export const CACHE_KEYS = {
  PLATFORM_STATS: 'platform:stats:v2',
  TRENDING_HUBS: 'hubs:trending:v1',
  FEATURED_HUBS: 'hubs:featured:v1',
  LEADERBOARD: 'hubs:leaderboard:v1',
  HUB_COUNTS: 'hubs:counts:v1',
} as const;

// Clean memory cache every 5 minutes
setInterval(
  () => {
    PerformanceCache.getInstance().cleanMemoryCache();
  },
  5 * 60 * 1000
);

export const cache = PerformanceCache.getInstance();
